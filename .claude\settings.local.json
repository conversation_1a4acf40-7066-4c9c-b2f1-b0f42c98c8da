{"permissions": {"allow": ["Bash(find:*)", "<PERSON><PERSON>(true)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "Bash(npm install:*)", "<PERSON><PERSON>(git clone:*)", "Bash(cp:*)", "mcp__serena__find_file", "mcp__serena__list_dir", "mcp__serena__activate_project", "mcp__serena__check_onboarding_performed", "mcp__serena__onboarding", "mcp__serena__find_symbol", "mcp__serena__get_symbols_overview", "mcp__serena__think_about_collected_information", "mcp__serena__write_memory", "mcp__serena__think_about_task_adherence", "mcp__serena__think_about_whether_you_are_done", "mcp__serena__read_memory", "mcp__serena__search_for_pattern", "mcp__serena__list_memories", "Bash(flutter analyze:*)", "Bash(grep:*)", "multiEdit", "task", "todo", "Bash(rg:*)", "<PERSON><PERSON>(cat:*)", "Bash(cd:*)", "Ba<PERSON>(flutter:*)", "<PERSON><PERSON>(sed:*)", "Bash(/mnt/c/flutter/bin/flutter pub get)", "Bash(/mnt/c/flutter/flutter/bin/flutter pub get)", "<PERSON><PERSON>(chmod:*)", "Bash(for file in lib/services/location_service.dart lib/providers/app_state_provider.dart lib/providers/auth_provider.dart)", "Bash(do)", "Bash(done)", "Bash(./scripts/team-health.sh:*)", "<PERSON><PERSON>(make:*)", "Bash(dart analyze:*)", "WebFetch(domain:firebase.google.com, docs.flutter.dev, docs.anthropic.com, dart.dev, github.com)", "Bash(/mnt/c/flutter/bin/flutter:*)", "Bash(/mnt/c/flutter/flutter/bin/flutter analyze --no-pub)", "<PERSON><PERSON>(test:*)", "Bash(/mnt/c/flutter/flutter/bin/dart analyze lib)", "Bash(npm search:*)", "Bash(pip search:*)", "Bash(pip install:*)", "Bash(pipx install:*)", "Bash(apt list:*)", "Bash(sudo apt install:*)", "Bash(timeout 30 git clone --depth 1 https://github.com/disler/nano-agent.git /tmp/nano-agent)", "Bash(/mnt/c/flutter/flutter/bin/flutter build apk --debug)", "<PERSON><PERSON>(./gradlew:*)", "Bash(node:*)", "Bash(claude add npx -y \"@modelcontextprotocol/server-sequential-thinking\")", "Bash(claude add \"@modelcontextprotocol/server-sequential-thinking\")", "Bash(claude list servers)", "<PERSON><PERSON>(claude mcp:*)", "Bash(export PATH=\"$HOME/.local/bin:$PATH\")", "Bash(uvx:*)", "Bash(dart pub run build_runner:*)", "Bash(/mnt/c/flutter/flutter/bin/flutter pub run build_runner build --delete-conflicting-outputs)"], "deny": [], "additionalDirectories": ["/tmp", "/home/<USER>/.claude"]}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["search1api-crawler", "Bright Data", "mcp-server-fetch", "universal_mcp", "Framelink Figma MCP", "server-memory", "firecrawl-mcp", "task-master-ai", "sequential-thinking", "think-mcp-server", "mcp-server-deep-research", "@21st-dev/magic"], "disableAllHooks": false, "outputStyle": "default"}